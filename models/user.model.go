package models

import (
	"time"
)

type User struct {
	BaseModelHardDelete
	Email       string  `json:"email" gorm:"column:email"`
	FullName    string  `json:"full_name" gorm:"column:full_name"`
	DisplayName string  `json:"display_name" gorm:"column:display_name"`
	Position    string  `json:"position" gorm:"column:position"`
	TeamCode    *string `json:"team_code" gorm:"column:team_code"`
	Company     *string `json:"company" gorm:"column:company"`
	AvatarURL   string  `json:"avatar_url" gorm:"column:avatar_url"`
	SlackID     *string `json:"slack_id" gorm:"column:slack_id"`
	IsActive    bool    `json:"is_active" gorm:"column:is_active"`

	JoinedDate *time.Time `json:"joined_date" gorm:"column:joined_date"`

	AccessLevel *UserAccessLevel `json:"access_level,omitempty" gorm:"foreignKey:UserID;references:ID"`
	Timesheets  []Timesheet      `json:"-" gorm:"foreignKey:UserID;references:ID"`
	Checkins    []Checkin        `json:"-" gorm:"foreignKey:UserId;references:ID"`
	Team        *Team            `json:"team,omitempty" gorm:"foreignKey:TeamCode;references:Code"`
}

func (User) TableName() string {
	return "users"
}
