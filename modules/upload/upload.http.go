package upload

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewUploadHTTP(e *echo.Echo) {
	upload := &UploadController{}
	// POST /uploads accepts multipart/form-data with key "file"
	e.POST("/uploads", core.WithHTTPContext(upload.Upload), middleware.AuthMiddleware())
	// GET /storage/* serves files from storage
	e.GET("/storage/*", core.WithHTTPContext(upload.GetFile))
}
