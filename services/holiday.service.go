package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IHolidayService interface {
	Create(input *HolidayCreatePayload) (*models.Holiday, core.IError)
	Update(id string, input *HolidayUpdatePayload) (*models.Holiday, core.IError)
	Find(id string) (*models.Holiday, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Holiday], core.IError)
	Delete(id string) core.IError
}

type holidayService struct {
	ctx core.IContext
}

func (s holidayService) Create(input *HolidayCreatePayload) (*models.Holiday, core.IError) {
	holiday := &models.Holiday{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		Name:                input.Name,
		Date:                input.Date,
		CreatedByID:         utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repo.Holiday(s.ctx).Create(holiday)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(holiday.ID)
}

func (s holidayService) Update(id string, input *HolidayUpdatePayload) (*models.Holiday, core.IError) {
	holiday, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.Name != "" {
		holiday.Name = input.Name
	}
	if !input.Date.IsZero() {
		holiday.Date = input.Date
	}

	holiday.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)

	ierr = repo.Holiday(s.ctx).Where("id = ?", id).Updates(holiday)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(holiday.ID)
}

func (s holidayService) Find(id string) (*models.Holiday, core.IError) {
	return repo.Holiday(s.ctx).FindOne("id = ?", id)
}

func (s holidayService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Holiday], core.IError) {
	return repo.Holiday(s.ctx, repo.HolidayOrderBy(pageOptions), repo.HolidayWithSearch(pageOptions.Q)).Pagination(pageOptions)
}

func (s holidayService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Holiday(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewHolidayService(ctx core.IContext) IHolidayService {
	return &holidayService{ctx: ctx}
}
