package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ITeamService interface {
	Create(input *TeamCreatePayload) (*models.Team, core.IError)
	Update(id string, input *TeamUpdatePayload) (*models.Team, core.IError)
	Find(id string) (*models.Team, core.IError)
	FindByCode(code string) (*models.Team, core.IError)
	Pagination(pageOptions *core.PageOptions, options *TeamPaginationOptions) (*repository.Pagination[models.Team], core.IError)
	Delete(id string) core.IError
}

type teamService struct {
	ctx core.IContext
}
type TeamPaginationOptions struct {
	TeamCode *string
}

func (s teamService) Create(input *TeamCreatePayload) (*models.Team, core.IError) {
	team := &models.Team{
		BaseModel:      models.NewBaseModel(),
		Name:           input.Name,
		Code:           input.Code,
		Description:    utils.ToPointer(input.Description),
		Color:          input.Color,
		WorkingStartAt: input.StartWorkingAt,
		WorkingEndAt:   input.EndWorkingAt,
		CreatedByID:    utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repo.Team(s.ctx).Create(team)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(team.ID)
}

func (s teamService) Update(id string, input *TeamUpdatePayload) (*models.Team, core.IError) {
	team, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.Name != "" {
		team.Name = input.Name
	}
	if input.Code != "" {
		team.Code = input.Code
	}
	if input.Description != "" {
		team.Description = utils.ToPointer(input.Description)
	}
	if input.Color != "" {
		team.Color = input.Color
	}

	if input.StartWorkingAt != nil {
		team.WorkingStartAt = input.StartWorkingAt
	}
	if input.EndWorkingAt != nil {
		team.WorkingEndAt = input.EndWorkingAt
	}

	team.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)

	ierr = repo.Team(s.ctx).Where("id = ?", id).Updates(team)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(team.ID)
}

func (s teamService) Find(id string) (*models.Team, core.IError) {
	return repo.Team(s.ctx).FindOne("id = ?", id)
}

func (s teamService) FindByCode(code string) (*models.Team, core.IError) {
	return repo.Team(s.ctx).FindOne("code = ?", code)
}

func (s teamService) Pagination(pageOptions *core.PageOptions, options *TeamPaginationOptions) (*repository.Pagination[models.Team], core.IError) {
	// Implementation for user pagination
	repoOptions := []repository.Option[models.Team]{
		repo.TeamWithTeamCode(options.TeamCode),
		repo.TeamWithSearch(pageOptions.Q),
		repo.TeamOrderBy(pageOptions),
	}

	return repo.Team(s.ctx, repoOptions...).Pagination(pageOptions)
}

func (s teamService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Team(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewTeamService(ctx core.IContext) ITeamService {
	return &teamService{ctx: ctx}
}
