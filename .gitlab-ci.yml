image: docker:stable
services:
  - docker:24.0.5-dind
variables:
  TAG_LATEST: $CI_REGISTRY_IMAGE:latest
  TAG_COMMIT: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
  TAG_PROD_LATEST: $CI_REGISTRY_IMAGE:latest-prod
  TAG_PROD_COMMIT: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-prod
  RELEASE_TOKEN: "**************************"
  # Sentry configuration
  SENTRY_PROJECT: "finework-api"  # Replace with your actual Sentry project name
  SENTRY_ORG: "finema"           # Replace with your actual Sentry organization
  SENTRY_AUTH_TOKEN: "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"  # Replace with your actual Sentry auth token
stages:
  - build
  - deploy
  - release

build-develop:
  stage: build
  only:
    - develop
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build --build-arg APP_RELEASE_VERSION=$CI_COMMIT_SHORT_SHA --build-arg APP_RELEASE_ENVIRONMENT=develop -t $TAG_COMMIT -t $TAG_LATEST .
    - docker push $TAG_COMMIT
    - docker push $TAG_LATEST
    - docker rmi $TAG_COMMIT $TAG_LATEST
  tags:
    - latest-runner
  environment:
    name: develop

deploy-develop:
  stage: deploy
  only:
    - develop
  image: alpine/git
  tags:
    - latest-runner
  before_script:
    - apk add --no-cache curl bash
    - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash
    - mv kustomize /usr/local/bin/
    - git config --global user.email "${GIT_USER_EMAIL:-$GITLAB_USER_EMAIL}"
    - git config --global user.name "${GIT_USER_NAME:-$GITLAB_USER_NAME}"
  script:
    - git clone https://gitlab-ci-token:${RELEASE_TOKEN}@${CI_SERVER_HOST}/finema/finework/finework-k8s.git
    - cd finework-k8s/overlays/develop
    # - kustomize edit set image $TAG_LATEST=$TAG_COMMIT
    - kustomize edit set image $CI_REGISTRY_IMAGE=$TAG_COMMIT
    - cat kustomization.yml
    - git commit -am '[skip ci] image update'
    - git push


  environment:
    name: develop

# Sentry Release Management
sentry-release-develop:
  stage: release
  only:
    - develop
  image: alpine:latest
  tags:
    - latest-runner
  before_script:
    - apk add --no-cache curl bash
    # Install sentry-cli
    - curl -sL https://sentry.io/get-cli/ | bash
  script:
    # Use commit SHA as version (predictable in CI)
    - export VERSION="$CI_COMMIT_SHORT_SHA"
    - export SENTRY_AUTH_TOKEN="$SENTRY_AUTH_TOKEN"
    - export SENTRY_ORG="$SENTRY_ORG"

    # Create a release with project specification (following Sentry docs)
    - sentry-cli releases new -p $SENTRY_PROJECT $VERSION

    # Associate commits with the release (following Sentry docs pattern)
    - sentry-cli releases set-commits --auto $VERSION

    # Mark the release as deployed to develop environment
    - sentry-cli releases deploys $VERSION new -e develop

    # Finalize the release
    - sentry-cli releases finalize $VERSION
  environment:
    name: develop
  dependencies:
    - deploy-develop
